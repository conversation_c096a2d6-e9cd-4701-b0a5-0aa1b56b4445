package com.eci.common;

import com.eci.common.cache.adapter.CacheAdapter; // 引入 CacheAdapter
import com.eci.common.cache.factory.CacheFactory;
import org.slf4j.Logger; // 建议添加日志
import org.slf4j.LoggerFactory; // 建议添加日志
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct; // 引入 PostConstruct
import java.util.List; // 引入 List
import java.util.concurrent.TimeUnit; // 引入 TimeUnit

/**
 * CacheFactory注入器
 * 用于在静态方法中获取Spring管理的CacheFactory实例
 */
@Component
public class CacheFactoryInjector {
    // [新增] 建议添加日志记录器
    private static final Logger logger = LoggerFactory.getLogger(CacheFactoryInjector.class);

    private static CacheFactory cacheFactory;

    @Autowired
    public void setCacheFactory(CacheFactory cacheFactory) {
        CacheFactoryInjector.cacheFactory = cacheFactory;
    }

    /**
     * [新增] 使用 @PostConstruct 来执行依赖注入后的初始化逻辑
     * 这个方法会在 setCacheFactory 执行完毕后由 Spring 自动调用。
     */
    @PostConstruct
    public void init() {
        logger.info("CacheFactoryInjector is initialized. Proceeding to initialize dependent static caches...");
        // 此时，静态字段 cacheFactory 已被Spring注入，可以安全使用
        if (cacheFactory != null) {
            try {
                @SuppressWarnings({"unchecked", "rawtypes"})
                CacheAdapter adapter = cacheFactory.createCache(
                        "dict-sql-result",
                        String.class,
                        List.class,
                        100, TimeUnit.MINUTES,  // 100分钟过期
                        100  // 最大100个条目
                );

                // 调用DictFieldUtils的静态setter方法，将创建好的adapter实例“注入”进去
                DictFieldUtils.setSqlResultCacheAdapter(adapter);

                logger.info("DictFieldUtils's CacheFactory adapter initialized successfully via CacheFactoryInjector. Cache type: {}",
                        adapter.getType());

            } catch (Exception e) {
                logger.warn("Failed to create and set CacheFactory adapter for DictFieldUtils. It will use local Caffeine cache.", e);
            }
        } else {
            logger.warn("CacheFactory is null after injection. DictFieldUtils will use its local Caffeine cache.");
        }
    }

    /**
     * 获取CacheFactory实例
     * @return CacheFactory实例，如果未初始化则返回null
     */
    public static CacheFactory getCacheFactory() {
        return cacheFactory;
    }

    /**
     * 检查CacheFactory是否已初始化
     * @return true如果已初始化，false否则
     */
    public static boolean isInitialized() {
        return cacheFactory != null;
    }
}

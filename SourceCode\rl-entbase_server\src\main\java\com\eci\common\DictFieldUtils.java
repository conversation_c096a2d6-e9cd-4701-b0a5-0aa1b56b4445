package com.eci.common;

import com.eci.common.DictField;
import com.eci.common.cache.adapter.CacheAdapter;
import com.eci.common.cache.factory.CacheFactory;
import com.eci.common.ZsrDBHelper;
import com.eci.common.CodeNameCommon;
import com.eci.common.SqlTemplate;
import com.eci.common.util.StringUtils;
import com.eci.page.TgPageInfo;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

/**
 * 字典处理工具类：自动将带有 @DictField 注解的字段映射为字典名称或格式化时间
 * <p>
 * 优化功能：
 * 1. 分离静态和动态缓存策略
 * 2. queryKey 和 SQL 查询的定时刷新机制
 * 3. 完善的 SQL 参数传递逻辑
 * 4. 精确的缓存键生成策略
 */
public class DictFieldUtils {

    private static final Logger logger = LoggerFactory.getLogger(DictFieldUtils.class);

    // 缓存：实体类 -> 带有 DictField 注解的字段和注解的映射
    private static final Map<Class<?>, Map<Field, DictField>> DICT_FIELD_CACHE = new ConcurrentHashMap<>();

    // 缓存：动态字典数据（queryKey、SQL 查询结果）-> 支持过期时间
    private static final Cache<String, Map<String, CodeNameCommon>> DYNAMIC_DICT_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(100, TimeUnit.MINUTES) // 设置缓存时间为 100 分钟
            .maximumSize(100)
            .build();

    // 缓存：静态字典数据（data 定义）-> 永久缓存，不过期，但有大小限制
    private static final Cache<String, Map<String, CodeNameCommon>> STATIC_DICT_CACHE = Caffeine.newBuilder()
            .maximumSize(1000) // 限制最大1000个条目
            .build();

    // 缓存：SQL 模板 -> 避免重复解析，添加大小限制防止内存泄露
    private static final Cache<String, SqlTemplate> SQL_TEMPLATE_CACHE = Caffeine.newBuilder()
            .maximumSize(500) // 限制最大500个SQL模板
            .expireAfterAccess(2, TimeUnit.HOURS) // 2小时未访问则过期
            .build();

    private static final ZsrDBHelper zsrDBHelper = new ZsrDBHelper();

    // 缓存：JSON 数组 -> 已解析的字典数据，添加大小限制防止内存泄露
    private static final Cache<Integer, Map<String, CodeNameCommon>> JSON_DATA_CACHE = Caffeine.newBuilder()
            .maximumSize(200) // 限制最大200个JSON数据缓存
            .build();

    // 缓存：SQL 执行结果 -> 支持过期时间
    private static final Cache<String, List<Map<String, Object>>> SQL_RESULT_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(100, TimeUnit.MINUTES) // 缩短为 100 分钟，配合定时刷新
            .maximumSize(100)
            .build();

    // CacheFactory管理的SQL结果缓存适配器
    private static CacheAdapter<String, List<Map<String, Object>>> sqlResultCacheAdapter;

    // 定时器：用于定时刷新 SQL 查询缓存
    private static final ScheduledExecutorService CACHE_REFRESH_EXECUTOR = Executors.newScheduledThreadPool(2, r -> {
        Thread t = new Thread(r, "DictFieldUtils-CacheRefresh");
        t.setDaemon(true);
        return t;
    });

    // 记录需要定时刷新的 SQL 查询
    private static final Set<String> SQL_REFRESH_KEYS = ConcurrentHashMap.newKeySet();

    // 记录定时任务，支持取消
    private static final Map<String, ScheduledFuture<?>> SCHEDULED_TASKS = new ConcurrentHashMap<>();

    // 记录 SQL 查询的最后刷新时间
    private static final Map<String, Long> SQL_LAST_REFRESH_TIME = new ConcurrentHashMap<>();

    // 静态初始化块：设置关闭钩子和缓存监控
    static {

        // 启动内存监控
        startMemoryMonitoring();

        // 注册缓存监控
        CacheMonitor.registerCache("DICT_FIELD_CACHE", DICT_FIELD_CACHE, 1000);
        CacheMonitor.registerCache("DYNAMIC_DICT_CACHE", DYNAMIC_DICT_CACHE, 100);
        CacheMonitor.registerCache("STATIC_DICT_CACHE", STATIC_DICT_CACHE, 1000);
        CacheMonitor.registerCache("SQL_TEMPLATE_CACHE", SQL_TEMPLATE_CACHE, 500);
        CacheMonitor.registerCache("SQL_RESULT_CACHE", SQL_RESULT_CACHE, 100);
        CacheMonitor.registerCache("JSON_DATA_CACHE", JSON_DATA_CACHE, 200);

        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                // 取消所有定时任务
                for (ScheduledFuture<?> task : SCHEDULED_TASKS.values()) {
                    task.cancel(false);
                }
                SCHEDULED_TASKS.clear();

                CACHE_REFRESH_EXECUTOR.shutdown();
                if (!CACHE_REFRESH_EXECUTOR.awaitTermination(5, TimeUnit.SECONDS)) {
                    CACHE_REFRESH_EXECUTOR.shutdownNow();
                }
                logger.info("DictFieldUtils 定时器已关闭");
            } catch (InterruptedException e) {
                CACHE_REFRESH_EXECUTOR.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }));
    }

    /**
     * 处理单个实体的字典字段
     */
    public static <T> void handleDictFields(T entity) {
        if (entity == null) return;
        handleDictFields(Collections.singletonList(entity));
    }

    /**
     * 处理实体列表的字典字段
     */
    public static <T> void handleDictFields(List<T> entities) {
        if (CollectionUtils.isEmpty(entities)) return;

        Class<?> entityClass = entities.get(0).getClass();
        Map<Field, DictField> annotatedFields = getAnnotatedFields(entityClass);
        if (annotatedFields.isEmpty()) return;

        // 查询字典数据
        Map<String, Map<String, CodeNameCommon>> dictDataMap = fetchDictData(annotatedFields.values());

        // 使用并行流处理实体
        entities.parallelStream().forEach(entity -> {
            if (entity != null) {
                annotatedFields.forEach((field, dictField) -> {
                    try {
                        processField(entity, field, dictField, dictDataMap);
                    } catch (Exception e) {
                        logger.error("处理字典字段异常：{}.{}", entityClass.getSimpleName(), field.getName(), e);
                    }
                });
            }
        });
    }

    /**
     * 获取所有带 DictField 注解的字段（包括父类）
     */
    private static Map<Field, DictField> getAnnotatedFields(Class<?> clazz) {
        return DICT_FIELD_CACHE.computeIfAbsent(clazz, cls -> {
            List<Field> allFields = getAllFields(cls);
            return allFields.stream()
                    .filter(f -> f.isAnnotationPresent(DictField.class))
                    .collect(Collectors.toMap(
                            f -> f,
                            f -> f.getAnnotation(DictField.class),
                            (f1, f2) -> f1 // 如果有重复字段，保留第一个
                    ));
        });
    }

    /**
     * 获取类及其父类的所有字段
     */
    private static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        Class<?> currentClass = clazz;
        while (currentClass != null && !currentClass.equals(Object.class)) {
            Collections.addAll(fields, currentClass.getDeclaredFields());
            currentClass = currentClass.getSuperclass();
        }
        return fields;
    }

    /**
     * 批量查询字典数据
     */
    private static Map<String, Map<String, CodeNameCommon>> fetchDictData(Collection<DictField> fields) {
        Map<String, Map<String, CodeNameCommon>> result = new HashMap<>();

        for (DictField dictField : fields) {
            String cacheKey = generateCacheKey(dictField);
            Map<String, CodeNameCommon> codeMap = null;

            try {
                if (StringUtils.isNotBlank(dictField.queryKey())) {
                    // queryKey 使用动态缓存，并注册定时刷新
                    codeMap = DYNAMIC_DICT_CACHE.getIfPresent(cacheKey);
                    if (codeMap == null) {
                        codeMap = DataDictUtils.queryCodeNameMap(dictField.queryKey());
                        DYNAMIC_DICT_CACHE.put(cacheKey, codeMap);

                        // 注册定时刷新
                        registerQueryKeyRefresh(cacheKey, dictField);
                    }
                } else if (dictField.data().length > 0) {
                    // data 使用静态缓存（永久缓存）
                    codeMap = STATIC_DICT_CACHE.get(cacheKey, key -> {
                        int dataHashCode = Arrays.hashCode(dictField.data());
                        return JSON_DATA_CACHE.get(dataHashCode, hash -> {
                            try {
                                return parseDataFromJsonArray(dictField.data());
                            } catch (Exception e) {
                                logger.warn("解析 JSON 字典数据失败", e);
                                return Collections.emptyMap();
                            }
                        });
                    });
                } else if (StringUtils.isNotBlank(dictField.sql())) {
                    // SQL 查询使用动态缓存，并注册定时刷新
                    codeMap = DYNAMIC_DICT_CACHE.getIfPresent(cacheKey);
                    if (codeMap == null) {
                        SqlTemplate sqlTemplate = SQL_TEMPLATE_CACHE.get(
                                dictField.sql(),
                                SqlTemplate::new
                        );
                        codeMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
                        codeMap.put("SQL_TEMPLATE", new CodeNameCommon("SQL_TEMPLATE", "SQL_TEMPLATE", sqlTemplate));
                        DYNAMIC_DICT_CACHE.put(cacheKey, codeMap);

                        // 注册定时刷新
                        registerSqlRefresh(cacheKey, dictField);
                    }
                }

                if (codeMap == null) {
                    codeMap = Collections.emptyMap();
                }
                result.put(cacheKey, codeMap);
            } catch (Exception e) {
                logger.warn("获取字典数据失败：{}", cacheKey, e);
                result.put(cacheKey, Collections.emptyMap());
            }
        }

        return result;
    }

    /**
     * 刷新类型枚举
     */
    private enum RefreshType {
        QUERY_KEY, SQL
    }

    /**
     * 注册 queryKey 的定时刷新
     */
    private static void registerQueryKeyRefresh(String cacheKey, DictField dictField) {
        registerDynamicCacheRefresh(cacheKey, dictField, RefreshType.QUERY_KEY);
    }

    /**
     * 注册 SQL 查询的定时刷新
     */
    private static void registerSqlRefresh(String cacheKey, DictField dictField) {
        registerDynamicCacheRefresh(cacheKey, dictField, RefreshType.SQL);
    }

    /**
     * 统一的动态缓存刷新注册方法
     */
    private static void registerDynamicCacheRefresh(String cacheKey, DictField dictField, RefreshType refreshType) {
        if (SQL_REFRESH_KEYS.add(cacheKey)) {
            // 只有第一次注册时才启动定时任务
            ScheduledFuture<?> task = CACHE_REFRESH_EXECUTOR.scheduleAtFixedRate(() -> {
                try {
                    switch (refreshType) {
                        case QUERY_KEY:
                            refreshQueryKeyCache(cacheKey, dictField);
                            break;
                        case SQL:
                            refreshSqlCache(cacheKey, dictField);
                            break;
                    }
                } catch (Exception e) {
                    logger.error("定时刷新 {} 缓存失败：{}", refreshType, cacheKey, e);
                }
            }, 1, 100, TimeUnit.MINUTES); // 改为100分钟刷新间隔

            // 保存任务引用，支持取消
            SCHEDULED_TASKS.put(cacheKey, task);
            logger.info("已注册 {} 定时刷新：{}", refreshType, cacheKey);
        }
    }

    /**
     * 刷新 queryKey 缓存
     */
    private static void refreshQueryKeyCache(String cacheKey, DictField dictField) {
        try {
            // 重新查询 queryKey 对应的字典数据
            Map<String, CodeNameCommon> newCodeMap = DataDictUtils.queryCodeNameMap(dictField.queryKey());
            DYNAMIC_DICT_CACHE.put(cacheKey, newCodeMap);
            logger.debug("已刷新 queryKey 缓存：{}", cacheKey);
        } catch (Exception e) {
            logger.warn("刷新 queryKey 缓存失败：{}", cacheKey, e);
        }
    }

    /**
     * 刷新 SQL 查询缓存
     */
    private static void refreshSqlCache(String cacheKey, DictField dictField) {
        try {
            // 记录刷新时间
            SQL_LAST_REFRESH_TIME.put(cacheKey, System.currentTimeMillis());

            SqlTemplate sqlTemplate = SQL_TEMPLATE_CACHE.getIfPresent(dictField.sql());
            if (sqlTemplate != null) {
                // 检查 SQL 是否包含参数
                List<String> paramNames = sqlTemplate.getParamNames();

                if (paramNames.isEmpty()) {
                    // 无参数 SQL：直接执行并缓存结果
                    try {
                        String sql = sqlTemplate.getRenderedSql();
                        String resultCacheKey = generateSqlResultCacheKey(sql, "");

                        // 执行 SQL 并缓存结果
                        List<Map<String, Object>> rows = executeDynamicSql(sql, Collections.emptyMap());

                        // 优先使用CacheFactory管理的缓存，如果不可用则使用本地缓存
                        CacheAdapter<String, List<Map<String, Object>>> cacheAdapter = getSqlResultCacheAdapter();
                        if (cacheAdapter != null && cacheAdapter.isAvailable()) {
                            cacheAdapter.put(resultCacheKey, rows);
                            logger.info("已刷新无参数 SQL 缓存到{}并重新执行查询：{}, 结果数量：{}",
                                cacheAdapter.getType(), cacheKey, rows.size());
                        } else {
                            SQL_RESULT_CACHE.put(resultCacheKey, rows);
                            logger.info("已刷新无参数 SQL 缓存到本地缓存并重新执行查询：{}, 结果数量：{}",
                                cacheKey, rows.size());
                        }
                    } catch (Exception e) {
                        logger.warn("刷新无参数 SQL 时执行查询失败：{}", cacheKey, e);
                    }
                } else {
                    // 有参数 SQL：清理结果缓存，等待下次查询时重新执行
                    CacheAdapter<String, List<Map<String, Object>>> cacheAdapter = getSqlResultCacheAdapter();
                    if (cacheAdapter != null && cacheAdapter.isAvailable()) {
                        cacheAdapter.clear();
                        logger.info("已刷新有参数 SQL 缓存，清理{}结果缓存：{}",
                            cacheAdapter.getType(), cacheKey);
                    } else {
                        SQL_RESULT_CACHE.invalidateAll();
                        logger.info("已刷新有参数 SQL 缓存，清理本地结果缓存：{}", cacheKey);
                    }
                }

                // 保持 SQL 模板缓存和字典缓存不变
                Map<String, CodeNameCommon> newCodeMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
                newCodeMap.put("SQL_TEMPLATE", new CodeNameCommon("SQL_TEMPLATE", "SQL_TEMPLATE", sqlTemplate));
                DYNAMIC_DICT_CACHE.put(cacheKey, newCodeMap);
            }
        } catch (Exception e) {
            logger.warn("刷新 SQL 缓存失败：{}", cacheKey, e);
        }
    }

    /**
     * 生成缓存 key
     */
    private static String generateCacheKey(DictField dictField) {
        if (StringUtils.isNotBlank(dictField.queryKey())) {
            return "QK_" + dictField.queryKey();
        } else if (dictField.data().length > 0) {
            return "DS_" + Arrays.hashCode(dictField.data());
        } else if (StringUtils.isNotBlank(dictField.sql())) {
            return "SQL_" + dictField.sql().hashCode();
        }
        return "EMPTY";
    }

    /**
     * 生成 SQL 结果缓存键
     */
    private static String generateSqlResultCacheKey(String sql, String paramsKey) {
        // 使用更安全的哈希计算方式
        int sqlHash = sql != null ? sql.hashCode() : 0;
        int paramsHash = paramsKey != null ? paramsKey.hashCode() : 0;

        // 组合哈希值，避免哈希冲突
        long combinedHash = ((long) sqlHash << 32) | (paramsHash & 0xFFFFFFFFL);

        return "SQL_RESULT_" + Long.toHexString(combinedHash);
    }

    /**
     * 处理单个字段
     */
    private static <T> void processField(T entity, Field field, DictField dictField,
                                         Map<String, Map<String, CodeNameCommon>> dictDataMap) throws Exception {
        field.setAccessible(true);
        Object value = field.get(entity);
        if (value == null) return;

        if (dictField.useDateFormat()) {
            String formattedDate = formatDateValue(value, dictField.dateFormat());
            if (formattedDate != null) {
                setFieldValue(entity, field.getName() + "Display", formattedDate);
            }
        } else {
            String cacheKey = generateCacheKey(dictField);
            Map<String, CodeNameCommon> codeMap = dictDataMap.get(cacheKey);

            if (codeMap != null) {
                CodeNameCommon templateEntry = codeMap.get("SQL_TEMPLATE");
                if (templateEntry != null && templateEntry.getSqlTemplate() != null) {
                    processSqlTemplate(entity, field, value, templateEntry.getSqlTemplate(), dictField);
                } else {
                    processCodeName(entity, field, value, codeMap, dictField);
                }
            }
        }
    }

    /**
     * 处理 SQL 模板
     */
    private static <T> void processSqlTemplate(T entity, Field field, Object value,
                                               SqlTemplate template, DictField dictField) throws Exception {
        Map<String, Object> params = new HashMap<>();

        // 优先使用注解中配置的 params，如果没有配置则使用 SQL 模板中解析出的参数
        String[] configuredParams = dictField.params();
        List<String> paramNames;

        if (configuredParams.length > 0) {
            // 使用注解中配置的参数
            paramNames = Arrays.asList(configuredParams);
            logger.debug("使用注解配置的参数：{}", Arrays.toString(configuredParams));
        } else {
            // 使用 SQL 模板中解析出的参数
            paramNames = template.getParamNames();
            logger.debug("使用 SQL 模板解析的参数：{}", paramNames);
        }

        // 验证 SQL 模板中的参数与配置的参数是否匹配
        Set<String> templateParams = new HashSet<>(template.getParamNames());
        Set<String> configParams = new HashSet<>(paramNames);
        if (!templateParams.equals(configParams)) {
            logger.warn("SQL 模板参数 {} 与配置参数 {} 不匹配，使用模板参数", templateParams, configParams);
            paramNames = template.getParamNames();
        }

        // 从实体中获取参数值
        for (String paramName : paramNames) {
            try {
                Field paramField = findFieldInClassHierarchy(entity.getClass(), paramName);
                if (paramField != null) {
                    paramField.setAccessible(true);
                    Object paramValue = paramField.get(entity);
                    params.put(paramName, paramValue);
                    logger.debug("参数 {} = {}", paramName, paramValue);
                } else {
                    logger.warn("在实体 {} 中未找到参数字段：{}", entity.getClass().getSimpleName(), paramName);
                    params.put(paramName, null);
                }
            } catch (Exception e) {
                logger.error("获取参数 {} 的值失败", paramName, e);
                params.put(paramName, null);
            }
        }

        // 构建缓存 key：SQL + 参数排序后的 hash
        String sql = template.getRenderedSql();
        String sortedParamsKey = params.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(e -> e.getKey() + "=" + (e.getValue() != null ? e.getValue().toString() : "null"))
                .collect(Collectors.joining("&"));

        // 使用更精确的缓存键生成策略
        String cacheKey = generateSqlResultCacheKey(sql, sortedParamsKey);

        List<Map<String, Object>> rows = SQL_RESULT_CACHE.get(cacheKey, k -> executeDynamicSql(sql, params));

        if (!rows.isEmpty()) {
            String name = getNameByCodeStreamFindFirst(rows, value.toString());
            if (name != null) {
                setFieldValue(entity, field.getName() + dictField.suffix(), name);
            }
        } else {
            logger.debug("SQL 查询无结果：{}", sql);
        }
    }

    /**
     * 在类层次结构中查找字段（包括父类）
     */
    private static Field findFieldInClassHierarchy(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null && !currentClass.equals(Object.class)) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }

    /**
     * 处理普通字典映射
     */
    private static <T> void processCodeName(T entity, Field field, Object value,
                                            Map<String, CodeNameCommon> codeMap, DictField dictField) throws Exception {
        String code = value.toString();
        CodeNameCommon common = codeMap.get(code);
        if (common != null) {
            setFieldValue(entity, field.getName() + dictField.suffix(), common.getName());
        }
    }

    /**
     * 执行动态 SQL
     */
    private static List<Map<String, Object>> executeDynamicSql(String sql, Map<String, Object> params) {
        try {
            return zsrDBHelper.queryForListWithNamedParams(sql, params);
        } catch (Exception e) {
            logger.error("执行动态 SQL 失败：{}", sql, e);
            return Collections.emptyList();
        }
    }

    /**
     * 格式化时间值
     */
    private static String formatDateValue(Object value, String pattern) {
        try {
            if (value instanceof LocalDateTime) {
                return ((LocalDateTime) value).format(DateTimeFormatter.ofPattern(pattern));
            } else if (value instanceof LocalDate) {
                return ((LocalDate) value).format(DateTimeFormatter.ofPattern(pattern));
            } else if (value instanceof Date) {
                return new java.text.SimpleDateFormat(pattern).format((Date) value);
            } else if (value instanceof String) {
                String str = (String) value;
                return str.length() >= 10 ? str.split("T| ")[0] : str;
            }
        } catch (Exception e) {
            logger.warn("时间格式化失败：{}", value, e);
        }
        return null;
    }

    /**
     * 设置字段值
     */
    private static <T> void setFieldValue(T entity, String fieldName, Object value) throws Exception {
        try {
            Field field = entity.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(entity, value);
        } catch (NoSuchFieldException e) {
            createAndSetField(entity, fieldName, value);
        }
    }

    /**
     * 动态创建字段并赋值
     */
    private static <T> void createAndSetField(T entity, String fieldName, Object value) throws Exception {
        if (entity instanceof ZsrBaseEntity) {
            ((ZsrBaseEntity) entity).push(fieldName, value);
            logger.debug("字段 {} 使用 ZsrBaseEntity 基类 put 值 {}", fieldName, value);
        } else {
            logger.warn("实体类 {} 不支持动态添加字段", entity.getClass().getName());
        }
    }

    /**
     * 解析 JSON 数组数据
     */
    private static Map<String, CodeNameCommon> parseDataFromJsonArray(String[] dataArray) {
        if (dataArray == null || dataArray.length == 0) {
            return Collections.emptyMap();
        }

        return Arrays.stream(dataArray)
                .map(json -> {
                    try {
                        ZsrJson zsrJson = ZsrJson.parse(json);
                        return new CodeNameCommon(
                                zsrJson.getString("code"),
                                zsrJson.getString("name")
                        );
                    } catch (Exception e) {
                        logger.warn("解析 JSON 字段失败：{}", json, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        CodeNameCommon::getCode,
                        common -> common,
                        (v1, v2) -> v1,
                        () -> new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
                ));
    }

    /**
     * 清理缓存的公共方法
     */
    public static void clearCache() {
        DYNAMIC_DICT_CACHE.invalidateAll();
        STATIC_DICT_CACHE.cleanUp();
        SQL_RESULT_CACHE.invalidateAll();
        JSON_DATA_CACHE.invalidateAll();
        SQL_TEMPLATE_CACHE.invalidateAll();
        SQL_LAST_REFRESH_TIME.clear();
        logger.info("已清理所有缓存");
    }

    /**
     * 检查 SQL 缓存是否需要刷新（用于调试）
     */
    public static boolean isSqlCacheRefreshed(String cacheKey) {
        Long lastRefreshTime = SQL_LAST_REFRESH_TIME.get(cacheKey);
        return lastRefreshTime != null && (System.currentTimeMillis() - lastRefreshTime) < 70000; // 70 秒内算作已刷新
    }



    /**
     * 根据 code 获取 name
     */
    public static String getNameByCodeStreamFindFirst(List<Map<String, Object>> dataList, String code) {
        return  dataList.stream()
                .filter(map -> {
                    Object value =Zsr. MapIgnoreCase.getIgnoreCase(map, "CODE");
                    return value != null && code.equals(value.toString());
                })
                .findFirst()
                .map(map -> (String) Zsr.MapIgnoreCase.getIgnoreCase(map, "NAME"))
                .orElse(null);
    }

    /**
     * 立即刷新所有缓存
     */
    public static void refreshCacheNow() {
        // 清理缓存
//        DYNAMIC_DICT_CACHE.invalidateAll();
        SQL_RESULT_CACHE.invalidateAll();

        // 更新 SQL 刷新时间
        for (String key : SQL_LAST_REFRESH_TIME.keySet()) {
            SQL_LAST_REFRESH_TIME.put(key, System.currentTimeMillis());
        }

        logger.info("已立即刷新所有 SQL 查询结果缓存");
    }

    /**
     * 立即刷新指定 SQL 的查询结果缓存（不带参数）
     */
    public static void refreshSqlResultsNow(String sql) {
        String cacheKey = generateSqlResultCacheKey(sql, "");
        SQL_RESULT_CACHE.invalidate(cacheKey);
        logger.info("已手动刷新 SQL 查询结果缓存：{}", cacheKey);
    }

    /**
     * 立即刷新指定 SQL 和参数的查询结果缓存
     */
    public static void refreshSqlResultsNow(String sql, Map<String, Object> params) {
        String sortedParamsKey = params.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(e -> e.getKey() + "=" + (e.getValue() != null ? e.getValue().toString() : "null"))
                .collect(Collectors.joining("&"));
        String cacheKey = generateSqlResultCacheKey(sql, sortedParamsKey);
        SQL_RESULT_CACHE.invalidate(cacheKey);
        logger.info("已手动刷新 SQL 查询结果缓存：{}", cacheKey);
    }

    /**
     * 立即刷新指定的缓存
     * @param cacheKey 缓存键
     */
    public static void refreshCacheNow(String cacheKey) {
        if (cacheKey.startsWith("QK_") || cacheKey.startsWith("SQL_")) {
            DYNAMIC_DICT_CACHE.invalidate(cacheKey);
        } else if (cacheKey.startsWith("DS_")) {
            STATIC_DICT_CACHE.cleanUp();
        }

        // 更新 SQL 刷新时间
        if (cacheKey.startsWith("SQL_")) {
            SQL_LAST_REFRESH_TIME.put(cacheKey, System.currentTimeMillis());
        }

        logger.info("已立即刷新缓存：{}", cacheKey);
    }

    /**
     * 立即刷新指定类型的缓存
     * @param refreshType 刷新类型
     */
    public static void refreshCacheNow(RefreshType refreshType) {
        switch (refreshType) {
            case QUERY_KEY:
                DYNAMIC_DICT_CACHE.asMap().keySet().stream()
                        .filter(k -> k.startsWith("QK_"))
                        .forEach(DYNAMIC_DICT_CACHE::invalidate);
                logger.info("已立即刷新 QUERY_KEY 类型缓存");
                break;
            case SQL:
                DYNAMIC_DICT_CACHE.asMap().keySet().stream()
                        .filter(k -> k.startsWith("SQL_"))
                        .forEach(DYNAMIC_DICT_CACHE::invalidate);
                SQL_RESULT_CACHE.invalidateAll();
                SQL_LAST_REFRESH_TIME.replaceAll((k, v) -> System.currentTimeMillis());
                logger.info("已立即刷新 SQL 类型缓存");
                break;
            default:
                logger.warn("不支持的刷新类型：{}", refreshType);
        }
    }

    /**
     * 取消指定缓存键的定时刷新任务
     */
    public static void cancelRefreshTask(String cacheKey) {
        ScheduledFuture<?> task = SCHEDULED_TASKS.remove(cacheKey);
        if (task != null) {
            task.cancel(false);
            SQL_REFRESH_KEYS.remove(cacheKey);
            logger.info("已取消定时刷新任务：{}", cacheKey);
        }
    }

    /**
     * 获取当前活跃的定时任务数量
     */
    public static int getActiveTaskCount() {
        return SCHEDULED_TASKS.size();
    }

    /**
     * 获取缓存统计信息
     */
    public static String getCacheStats() {
        return String.format(
            "DictFieldUtils缓存统计 - 字段缓存: %d, 动态缓存: %d, 静态缓存: %d, SQL模板: %d, SQL结果: %d, JSON缓存: %d, 活跃任务: %d",
            DICT_FIELD_CACHE.size(),
            DYNAMIC_DICT_CACHE.estimatedSize(),
            STATIC_DICT_CACHE.estimatedSize(),
            SQL_TEMPLATE_CACHE.estimatedSize(),
            SQL_RESULT_CACHE.estimatedSize(),
            JSON_DATA_CACHE.estimatedSize(),
            SCHEDULED_TASKS.size()
        );
    }


    /**
     * [修改点] 新增的公开静态方法，用于从外部（Spring Bean）设置缓存适配器。
     * 这是连接 Spring 和这个静态类的桥梁。
     * @param adapter 由 Spring 初始化后传入的缓存适配器实例
     */
    public static void setSqlResultCacheAdapter(CacheAdapter<String, List<Map<String, Object>>> adapter) {
        if (adapter != null) {
            DictFieldUtils.sqlResultCacheAdapter = adapter;
        }
    }

    /**
     * 获取SQL结果缓存适配器，如果CacheFactory未初始化则返回null
     */
    private static CacheAdapter<String, List<Map<String, Object>>> getSqlResultCacheAdapter() {
        return sqlResultCacheAdapter;
    }

    /**
     * 内存安全检查和清理
     */
    public static void performMemorySafetyCheck() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            double memoryUsagePercent = (double) usedMemory / totalMemory * 100;

            String usagePercentStr = String.format("%.2f", memoryUsagePercent);

            logger.info("内存使用情况 - 总内存: {}MB, 已用: {}MB, 使用率: {}%",
                    totalMemory / 1024 / 1024,
                    usedMemory / 1024 / 1024,
                    usagePercentStr);

            // 如果内存使用率超过80%，执行缓存清理
            if (memoryUsagePercent > 80.0) {
                logger.warn("内存使用率过高 ({}%), 开始执行缓存清理", usagePercentStr);
                performEmergencyCleanup();
            }
        } catch (Exception e) {
            logger.error("内存安全检查失败", e);
        }
    }

    /**
     * 紧急缓存清理
     */
    private static void performEmergencyCleanup() {
        try {
            // 清理动态缓存中的过期项
            DYNAMIC_DICT_CACHE.cleanUp();
            STATIC_DICT_CACHE.cleanUp();
            SQL_RESULT_CACHE.cleanUp();
            SQL_TEMPLATE_CACHE.cleanUp();
            JSON_DATA_CACHE.cleanUp();

            // 如果CacheFactory缓存可用，也进行清理
            CacheAdapter<String, List<Map<String, Object>>> cacheAdapter = getSqlResultCacheAdapter();
            if (cacheAdapter != null && cacheAdapter.isAvailable()) {
                // 不完全清空，只清理一半的缓存
                long currentSize = cacheAdapter.size();
                if (currentSize > 50) {
                    logger.info("清理CacheFactory缓存，当前大小: {}", currentSize);
                    // 这里可以实现更智能的清理策略
                }
            }

            // 建议JVM进行垃圾回收
            System.gc();

            logger.info("紧急缓存清理完成");
        } catch (Exception e) {
            logger.error("紧急缓存清理失败", e);
        }
    }

    /**
     * 启动内存监控定时任务
     */
    private static void startMemoryMonitoring() {
        CACHE_REFRESH_EXECUTOR.scheduleAtFixedRate(() -> {
            try {
                performMemorySafetyCheck();
            } catch (Exception e) {
                logger.error("内存监控任务执行失败", e);
            }
        }, 5, 30, TimeUnit.MINUTES); // 每30分钟检查一次内存使用情况
    }

}
